export interface PackVariant {
  id: string;
  label: string;
  value: string;
  isActive: boolean;
}

export interface ProductCardV1Props {
  // Product basic info
  id: string;
  title: string;
  image: string;
  price: number;
  originalPrice?: number;
  primaryColor: string;
  
  // Pack variants
  packVariants: PackVariant[];
  activePackId: string;
  
  // Cart state
  quantity: number;
  
  // Event handlers
  onPackSelect: (packId: string) => void;
  onAddToCart: () => void;
  onQuantityChange: (quantity: number) => void;
  
  // Optional props
  className?: string;
  showSameDayDelivery?: boolean;
  discountPercentage?: number;
}

export interface UseProductCardReturn {
  isAddedToCart: boolean;
  handleAddToCart: () => void;
  handleIncrement: () => void;
  handleDecrement: () => void;
  handlePackSelect: (packId: string) => void;
  getPackButtonClass: (pack: PackVariant) => string;
  getPackButtonStyle: (pack: PackVariant) => React.CSSProperties;
}
