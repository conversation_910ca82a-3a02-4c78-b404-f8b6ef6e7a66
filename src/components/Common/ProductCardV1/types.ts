import { Product } from "@/components/Common/ProductCard";

export interface PackVariant {
  id: string;
  label: string;
  value: string;
  isActive: boolean;
}

export interface ProductCardV1Props {
  product: Product;
  className?: string;
}

export interface UseProductCardReturn {
  selectedVariant: any;
  setSelectedVariant: (variant: any) => void;
  variantCounts: Record<string, number>;
  variantAdded: Record<string, boolean>;
  isAddedToCart: boolean;
  currentCount: number;
  handleAddToCart: () => void;
  handleIncrement: () => void;
  handleDecrement: () => void;
  getPackButtonClass: (variant: any, isActive: boolean) => string;
  getPackButtonStyle: (
    isActive: boolean,
    primaryColor: string
  ) => React.CSSProperties;
}
