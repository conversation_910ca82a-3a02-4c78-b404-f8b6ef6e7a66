"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { cn } from "@/libs/utils";
import { SameDayDelivery } from "@/assets/icons/SDD";
import Plus from "@/assets/icons/Plus";
import Minus from "@/assets/icons/Minus";
import { ProductCardV1Props } from "./types";
import { useProductCard } from "./hooks/useProductCard";

/**
 * ProductCardV1 Component
 *
 * A reusable product card component extracted from ProductCarouselSection.
 * Features pack selection, add to cart functionality, and quantity management.
 * Works with the existing Product interface like AccentProductCard.
 *
 * @param props - ProductCardV1Props
 * @returns JSX.Element
 */
const ProductCardV1: React.FC<ProductCardV1Props> = ({
  product,
  className,
}) => {
  // Default color if primaryColor is not provided
  const primaryColor = product.primaryColor || "#93385D";

  const {
    selectedVariant,
    setSelectedVariant,
    isAddedToCart,
    currentCount,
    handleAddToCart,
    handleIncrement,
    handleDecrement,
    getPackButtonClass,
    getPackButtonStyle,
  } = useProductCard(product);

  // Calculate discount percentage
  const calculatedDiscountPercentage =
    product.originalPrice && product.originalPrice > product.price
      ? Math.round(
          ((product.originalPrice - product.price) / product.originalPrice) *
            100
        )
      : 0;

  return (
    <div className={cn("w-[278px] flex-shrink-0 rounded-[10px]", className)}>
      {/* Product Image */}
      <div className="aspect-square relative">
        <Image
          src={selectedVariant?.image || product.image}
          alt={product.title}
          fill
          objectFit="contain"
          className="overflow-hidden rounded-t-[10px]"
        />
      </div>

      {/* Product Content */}
      <div
        className="rounded-b-[10px] flex flex-col p-4 text-white"
        style={{
          backgroundColor: primaryColor || "transparent",
          minHeight: "200px", // Fixed minimum height for consistency
        }}
      >
        <div className="flex flex-col h-full text-white">
          {/* Product Title - Fixed height container */}
          <div className="h-[52px] mb-2">
            {" "}
            {/* Fixed height for 2 lines */}
            <Link
              href={`/products/${product.id}`}
              className="text-lg leading-6.5 font-narrow font-semibold overflow-ellipsis break-words line-clamp-2 overflow-hidden block h-full"
            >
              {product.title}
            </Link>
          </div>

          {/* Same Day Delivery - Fixed height container */}
          <div className="h-[24px] mb-2">
            {" "}
            {/* Fixed height whether shown or not */}
            {product.sameDayDelivery ? (
              <SameDayDelivery color={primaryColor} height={24} width={127} />
            ) : (
              <div className="h-[24px]" /> // Placeholder to maintain height
            )}
          </div>

          {/* Pricing - Fixed height container */}
          <div className="h-[20px] mb-2">
            {" "}
            {/* Fixed height for pricing */}
            <div className="text-white font-obviously text-[13px] leading-5 font-medium space-x-1">
              {product.originalPrice &&
                product.originalPrice > product.price && (
                  <span className="line-through">₹{product.originalPrice}</span>
                )}
              <span>₹{selectedVariant?.price || product.price}</span>
              {calculatedDiscountPercentage > 0 && (
                <span>({calculatedDiscountPercentage}% OFF)</span>
              )}
            </div>
          </div>

          {/* Spacer to push pack selection and cart to bottom */}
          <div className="flex-grow"></div>

          {/* Pack Selection and Cart Actions - Fixed at bottom */}
          <div className="space-y-2 mt-auto">
            {/* Pack Variants - Fixed height container */}
            <div className="flex gap-1 h-[30px]">
              {" "}
              {/* Fixed height for pack buttons */}
              {product.variants && product.variants.length > 0 ? (
                product.variants.map((variant) => (
                  <button
                    key={variant.id}
                    onClick={() => setSelectedVariant(variant)}
                    className={getPackButtonClass(
                      variant,
                      selectedVariant?.id === variant.id
                    )}
                    style={getPackButtonStyle(
                      selectedVariant?.id === variant.id,
                      primaryColor
                    )}
                  >
                    {variant.size}
                  </button>
                ))
              ) : (
                // Default pack variants when no variants exist
                <>
                  <button
                    className={getPackButtonClass(null, true)}
                    style={getPackButtonStyle(true, primaryColor)}
                  >
                    pack of 1 kg
                  </button>
                  <button
                    className={getPackButtonClass(null, false)}
                    style={getPackButtonStyle(false, primaryColor)}
                  >
                    pack of 2 kg
                  </button>
                </>
              )}
            </div>

            {/* Add to Cart / Quantity Selector - Fixed height */}
            <div className="h-[32px]">
              {" "}
              {/* Fixed height for cart button */}
              {!isAddedToCart ? (
                <button
                  onClick={handleAddToCart}
                  className="bg-white h-8 font-obviously flex items-center justify-center rounded-sm cursor-pointer w-full py-0.5 px-4 border border-white text-xs leading-8 font-semibold"
                  style={{ color: primaryColor }}
                >
                  Add to cart
                </button>
              ) : (
                <button
                  className="bg-white h-8 font-obviously flex items-center justify-between rounded-sm cursor-pointer w-full py-2 px-4 border border-white text-xs leading-8 font-semibold"
                  style={{ color: primaryColor }}
                >
                  <div
                    className="p-2 flex items-center justify-center"
                    onClick={handleDecrement}
                  >
                    <Minus color={primaryColor} />
                  </div>
                  <span className="p-2 text-sm leading-4 font-bold">
                    {currentCount}
                  </span>
                  <div
                    className="p-2 flex items-center justify-center"
                    onClick={handleIncrement}
                  >
                    <Plus color={primaryColor} />
                  </div>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductCardV1;
