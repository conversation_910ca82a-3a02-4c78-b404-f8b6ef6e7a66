"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { cn } from "@/libs/utils";
import { SameDayDelivery } from "@/assets/icons/SDD";
import Plus from "@/assets/icons/Plus";
import Minus from "@/assets/icons/Minus";
import { ProductCardV1Props } from "./types";
import { useProductCard } from "./hooks/useProductCard";

/**
 * ProductCardV1 Component
 * 
 * A reusable product card component extracted from ProductCarouselSection.
 * Features pack selection, add to cart functionality, and quantity management.
 * 
 * @param props - ProductCardV1Props
 * @returns JSX.Element
 */
const ProductCardV1: React.FC<ProductCardV1Props> = ({
  id,
  title,
  image,
  price,
  originalPrice,
  primaryColor,
  packVariants,
  activePackId,
  quantity,
  onPackSelect,
  onAddToCart,
  onQuantityChange,
  className,
  showSameDayDelivery = true,
  discountPercentage,
}) => {
  const {
    isAddedToCart,
    handleAddToCart,
    handleIncrement,
    handleDecrement,
    handlePackSelect,
    getPackButtonClass,
    getPackButtonStyle,
  } = useProductCard({
    quantity,
    activePackId,
    primaryColor,
    packVariants,
    onPackSelect,
    onAddToCart,
    onQuantityChange,
  });

  // Calculate discount percentage if not provided
  const calculatedDiscountPercentage = 
    discountPercentage || 
    (originalPrice && originalPrice > price 
      ? Math.round(((originalPrice - price) / originalPrice) * 100)
      : 0);

  return (
    <div className={cn("w-[278px] flex-shrink-0 rounded-[10px]", className)}>
      {/* Product Image */}
      <div className="aspect-square relative">
        <Image
          src={image}
          alt={title}
          fill
          objectFit="contain"
          className="overflow-hidden rounded-t-[10px]"
        />
      </div>

      {/* Product Content */}
      <div
        className="rounded-b-[10px] h-full flex flex-col gap-2 p-4 text-white"
        style={{
          backgroundColor: primaryColor || "transparent",
        }}
      >
        <div className="flex flex-col gap-2 justify-between h-full text-white">
          {/* Product Title */}
          <Link
            href={`/products/${id}`}
            className="text-lg leading-6.5 font-narrow font-semibold overflow-ellipsis break-words line-clamp-2 overflow-hidden"
          >
            {title}
          </Link>

          {/* Same Day Delivery */}
          {showSameDayDelivery && (
            <SameDayDelivery
              color={primaryColor}
              height={24}
              width={127}
            />
          )}

          {/* Pricing */}
          <div className="text-white font-obviously text-[13px] leading-5 font-medium space-x-1">
            {originalPrice && originalPrice > price && (
              <span className="line-through">₹{originalPrice}</span>
            )}
            <span>₹{price}</span>
            {calculatedDiscountPercentage > 0 && (
              <span>({calculatedDiscountPercentage}% OFF)</span>
            )}
          </div>

          {/* Pack Selection and Cart Actions */}
          <div className="space-y-2">
            {/* Pack Variants */}
            <div className="flex gap-1">
              {packVariants.map((pack) => (
                <button
                  key={pack.id}
                  onClick={() => handlePackSelect(pack.id)}
                  className={getPackButtonClass(pack)}
                  style={getPackButtonStyle(pack)}
                >
                  {pack.label}
                </button>
              ))}
            </div>

            {/* Add to Cart / Quantity Selector */}
            {!isAddedToCart ? (
              <button
                onClick={handleAddToCart}
                className="bg-white h-8 font-obviously flex items-center justify-center rounded-sm cursor-pointer w-full py-0.5 px-4 border border-white text-xs leading-8 font-semibold"
                style={{ color: primaryColor }}
              >
                Add to cart
              </button>
            ) : (
              <button
                className="bg-white h-8 font-obviously flex items-center justify-between rounded-sm cursor-pointer w-full py-2 px-4 border border-white text-xs leading-8 font-semibold"
                style={{ color: primaryColor }}
              >
                <div 
                  className="p-2 flex items-center justify-center"
                  onClick={handleDecrement}
                >
                  <Minus color={primaryColor} />
                </div>
                <span className="p-2 text-sm leading-4 font-bold">
                  {quantity}
                </span>
                <div 
                  className="p-2 flex items-center justify-center"
                  onClick={handleIncrement}
                >
                  <Plus color={primaryColor} />
                </div>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductCardV1;
