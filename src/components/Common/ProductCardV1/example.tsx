"use client";

import React from "react";
import ProductCardV1 from "./index";
import { Product } from "@/components/Common/ProductCard";

/**
 * Example usage of ProductCardV1 component
 * This demonstrates how to integrate the component with the Product interface
 */
const ProductCardV1Example: React.FC = () => {
  // Sample product data using the existing Product interface
  const sampleProduct: Product = {
    id: "cold-coffee-protein",
    title: "Cold Coffee 15 g Protein Powder - Pack of 1 KG",
    image: "/product-double-cocoa.png",
    price: 4443,
    originalPrice: 4799,
    rating: 4.5,
    weight: "1 KG",
    primaryColor: "#036A38",
    enableProductBg: true,
    sameDayDelivery: true,
    deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
    variants: [
      {
        id: "variant-1kg",
        size: "pack of 1 kg",
        price: 4443,
        originalPrice: 4799,
        weight: "1kg",
        image: "/product-double-cocoa.png",
      },
      {
        id: "variant-2kg",
        size: "pack of 2 kg",
        price: 8443,
        originalPrice: 8999,
        weight: "2kg",
        image: "/product-double-cocoa-2kg.png",
      },
    ],
  };

  // Sample product without variants
  const sampleProductNoVariants: Product = {
    id: "simple-protein",
    title: "Simple Protein Powder - Single Pack",
    image: "/product-simple.png",
    price: 2999,
    originalPrice: 3299,
    rating: 4.2,
    weight: "500g",
    primaryColor: "#D23C47",
    enableProductBg: true,
    sameDayDelivery: true,
    deliveryTime: { hours: 3, minutes: 30, seconds: 0 },
  };

  return (
    <div className="p-8 bg-gray-100">
      <h2 className="text-2xl font-bold mb-4">ProductCardV1 Examples</h2>

      <div className="flex gap-4 flex-wrap">
        {/* Product with variants */}
        <div>
          <h3 className="text-lg font-semibold mb-2">With Variants</h3>
          <ProductCardV1 product={sampleProduct} />
        </div>

        {/* Product without variants */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Without Variants</h3>
          <ProductCardV1 product={sampleProductNoVariants} />
        </div>
      </div>

      {/* Usage Info */}
      <div className="mt-8 p-4 bg-white rounded-lg">
        <h3 className="font-bold mb-2">Usage:</h3>
        <pre className="text-sm bg-gray-100 p-2 rounded">
          {`<ProductCardV1 product={productData} />`}
        </pre>
        <p className="mt-2 text-sm text-gray-600">
          The component automatically handles pack selection, cart state, and
          variant management internally. Just pass a Product object with the
          existing interface.
        </p>
      </div>
    </div>
  );
};

export default ProductCardV1Example;
