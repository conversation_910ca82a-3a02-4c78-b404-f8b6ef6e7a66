"use client";

import React from "react";
import ProductCardV1 from "./index";
import { Product } from "@/components/Common/ProductCard";

/**
 * Example usage of ProductCardV1 component
 * This demonstrates how to integrate the component with the Product interface
 */
const ProductCardV1Example: React.FC = () => {
  // Sample product data using the existing Product interface
  const sampleProduct: Product = {
    id: "cold-coffee-protein",
    title: "Cold Coffee 15 g Protein Powder - Pack of 1 KG",
    image: "/product-double-cocoa.png",
    price: 4443,
    originalPrice: 4799,
    rating: 4.5,
    weight: "1 KG",
    primaryColor: "#036A38",
    enableProductBg: true,
    sameDayDelivery: true,
    deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
    variants: [
      {
        id: "variant-1kg",
        size: "pack of 1 kg",
        price: 4443,
        originalPrice: 4799,
        weight: "1kg",
        image: "/product-double-cocoa.png",
      },
      {
        id: "variant-2kg",
        size: "pack of 2 kg",
        price: 8443,
        originalPrice: 8999,
        weight: "2kg",
        image: "/product-double-cocoa-2kg.png",
      },
    ],
  };

  // Sample product without variants
  const sampleProductNoVariants: Product = {
    id: "simple-protein",
    title: "Simple Protein Powder - Single Pack",
    image: "/product-simple.png",
    price: 2999,
    originalPrice: 3299,
    rating: 4.2,
    weight: "500g",
    primaryColor: "#D23C47",
    enableProductBg: true,
    sameDayDelivery: true,
    deliveryTime: { hours: 3, minutes: 30, seconds: 0 },
  };

  // Sample product with short title and no same day delivery
  const sampleProductShortTitle: Product = {
    id: "short-title-protein",
    title: "Protein Bar",
    image: "/product-bar.png",
    price: 1999,
    rating: 4.0,
    weight: "60g",
    primaryColor: "#8B5CF6",
    enableProductBg: true,
    sameDayDelivery: false, // No same day delivery
  };

  // Sample product with no discount
  const sampleProductNoDiscount: Product = {
    id: "no-discount-protein",
    title: "Premium Whey Protein Isolate with Advanced Formula",
    image: "/product-premium.png",
    price: 5999,
    // No originalPrice = no discount
    rating: 4.8,
    weight: "2kg",
    primaryColor: "#059669",
    enableProductBg: true,
    sameDayDelivery: true,
    deliveryTime: { hours: 2, minutes: 0, seconds: 0 },
  };

  return (
    <div className="p-8 bg-gray-100">
      <h2 className="text-2xl font-bold mb-4">
        ProductCardV1 Examples - Consistent Heights
      </h2>

      <div className="flex gap-4 flex-wrap">
        {/* Product with variants */}
        <div>
          <h3 className="text-lg font-semibold mb-2">With Variants</h3>
          <ProductCardV1 product={sampleProduct} />
        </div>

        {/* Product without variants */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Without Variants</h3>
          <ProductCardV1 product={sampleProductNoVariants} />
        </div>

        {/* Product with short title and no same day delivery */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Short Title + No SDD</h3>
          <ProductCardV1 product={sampleProductShortTitle} />
        </div>

        {/* Product with no discount */}
        <div>
          <h3 className="text-lg font-semibold mb-2">No Discount</h3>
          <ProductCardV1 product={sampleProductNoDiscount} />
        </div>
      </div>

      {/* Usage Info */}
      <div className="mt-8 p-4 bg-white rounded-lg">
        <h3 className="font-bold mb-2">Fixed Height Features:</h3>
        <ul className="text-sm text-gray-600 space-y-1 mb-4">
          <li>
            • <strong>Consistent Heights:</strong> All cards maintain the same
            height regardless of content
          </li>
          <li>
            • <strong>Fixed Title Area:</strong> 52px height for product titles
            (supports up to 2 lines)
          </li>
          <li>
            • <strong>Same Day Delivery:</strong> 24px reserved space whether
            shown or hidden
          </li>
          <li>
            • <strong>Pricing Section:</strong> 20px fixed height for price
            information
          </li>
          <li>
            • <strong>Bottom Alignment:</strong> Pack selection and cart buttons
            always at bottom
          </li>
        </ul>

        <h3 className="font-bold mb-2">Usage:</h3>
        <pre className="text-sm bg-gray-100 p-2 rounded">
          {`<ProductCardV1 product={productData} />`}
        </pre>
        <p className="mt-2 text-sm text-gray-600">
          The component automatically handles pack selection, cart state, and
          variant management internally. Just pass a Product object with the
          existing interface.
        </p>
      </div>
    </div>
  );
};

export default ProductCardV1Example;
