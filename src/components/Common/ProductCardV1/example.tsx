"use client";

import React, { useState } from "react";
import ProductCardV1 from "./index";
import { PackVariant } from "./types";

/**
 * Example usage of ProductCardV1 component
 * This demonstrates how to integrate the component with state management
 */
const ProductCardV1Example: React.FC = () => {
  // Sample product data
  const [productState, setProductState] = useState({
    activePackId: "pack-1kg",
    quantity: 0,
  });

  // Sample pack variants
  const packVariants: PackVariant[] = [
    {
      id: "pack-1kg",
      label: "pack of 1 kg",
      value: "1kg",
      isActive: productState.activePackId === "pack-1kg",
    },
    {
      id: "pack-2kg",
      label: "pack of 2 kg", 
      value: "2kg",
      isActive: productState.activePackId === "pack-2kg",
    },
  ];

  // Event handlers
  const handlePackSelect = (packId: string) => {
    setProductState(prev => ({
      ...prev,
      activePackId: packId,
    }));
    console.log("Pack selected:", packId);
  };

  const handleAddToCart = () => {
    setProductState(prev => ({
      ...prev,
      quantity: 1,
    }));
    console.log("Added to cart");
  };

  const handleQuantityChange = (quantity: number) => {
    setProductState(prev => ({
      ...prev,
      quantity,
    }));
    console.log("Quantity changed:", quantity);
  };

  return (
    <div className="p-8 bg-gray-100">
      <h2 className="text-2xl font-bold mb-4">ProductCardV1 Example</h2>
      
      <div className="flex gap-4">
        <ProductCardV1
          id="cold-coffee-protein"
          title="Cold Coffee 15 g Protein Powder - Pack of 1 KG"
          image="/product-double-cocoa.png"
          price={4443}
          originalPrice={4799}
          primaryColor="#036A38"
          packVariants={packVariants}
          activePackId={productState.activePackId}
          quantity={productState.quantity}
          onPackSelect={handlePackSelect}
          onAddToCart={handleAddToCart}
          onQuantityChange={handleQuantityChange}
          showSameDayDelivery={true}
        />
      </div>

      {/* Debug Info */}
      <div className="mt-8 p-4 bg-white rounded-lg">
        <h3 className="font-bold mb-2">Current State:</h3>
        <pre className="text-sm">
          {JSON.stringify(productState, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default ProductCardV1Example;
