import { useCallback, useMemo, useState, useEffect } from "react";
import { cn } from "@/libs/utils";
import { Product } from "@/components/Common/ProductCard";
import { UseProductCardReturn } from "../types";

export const useProductCard = (product: Product): UseProductCardReturn => {
  // State for selected variant (similar to AccentProductCard)
  const [selectedVariant, setSelectedVariant] = useState<any>(
    product.variants && product.variants.length > 0
      ? product.variants[0]
      : undefined
  );

  // State for cart management (similar to AccentProductAddToCart)
  const [variantCounts, setVariantCounts] = useState<Record<string, number>>(
    {}
  );
  const [variantAdded, setVariantAdded] = useState<Record<string, boolean>>({});

  // Update selectedVariant when product.variants change
  useEffect(() => {
    if (product.variants && product.variants.length > 0) {
      if (selectedVariant) {
        const matchingVariant = product.variants.find(
          (v) => v.size === selectedVariant.size
        );
        if (matchingVariant) {
          setSelectedVariant(matchingVariant);
        } else {
          setSelectedVariant(product.variants[0]);
        }
      } else {
        setSelectedVariant(product.variants[0]);
      }
    } else {
      setSelectedVariant(undefined);
    }
  }, [product.variants, selectedVariant?.id]);

  // Get variant key for state management
  const variantKey = selectedVariant?.id || "default";
  const currentCount = variantCounts[variantKey] || 1;
  const isAdded = variantAdded[variantKey] || false;
  const isAddedToCart = useMemo(() => isAdded, [isAdded]);

  // Handle add to cart
  const handleAddToCart = useCallback(() => {
    setVariantAdded((prev) => ({
      ...prev,
      [variantKey]: true,
    }));
    console.log(`Added product ${product.id} to cart`);
  }, [variantKey, product.id]);

  // Handle quantity increment
  const handleIncrement = useCallback(() => {
    setVariantCounts((prev) => ({
      ...prev,
      [variantKey]: (prev[variantKey] || 1) + 1,
    }));
  }, [variantKey]);

  // Handle quantity decrement
  const handleDecrement = useCallback(() => {
    const currentVariantCount = variantCounts[variantKey] || 1;

    if (currentVariantCount > 1) {
      setVariantCounts((prev) => ({
        ...prev,
        [variantKey]: currentVariantCount - 1,
      }));
    } else {
      setVariantAdded((prev) => ({
        ...prev,
        [variantKey]: false,
      }));
    }
  }, [variantKey, variantCounts]);

  // Get pack button class
  const getPackButtonClass = useCallback((variant: any, isActive: boolean) => {
    return cn(
      "cursor-pointer flex-1 border-[1.5px] border-white rounded-[5px] text-[10px] font-obviously h-7.5 px-2 py-1 font-semibold uppercase",
      isActive && "bg-white"
    );
  }, []);

  // Get pack button style
  const getPackButtonStyle = useCallback(
    (isActive: boolean, primaryColor: string) => {
      if (isActive) {
        return { color: primaryColor };
      }
      return { color: "white" };
    },
    []
  );

  return {
    selectedVariant,
    setSelectedVariant,
    variantCounts,
    variantAdded,
    isAddedToCart,
    currentCount,
    handleAddToCart,
    handleIncrement,
    handleDecrement,
    getPackButtonClass,
    getPackButtonStyle,
  };
};
