import { useCallback, useMemo } from "react";
import { cn } from "@/libs/utils";
import { PackVariant, ProductCardV1Props, UseProductCardReturn } from "../types";

export const useProductCard = ({
  quantity,
  activePackId,
  primaryColor,
  packVariants,
  onPackSelect,
  onAddToCart,
  onQuantityChange,
}: Pick<
  ProductCardV1Props,
  | "quantity"
  | "activePackId"
  | "primaryColor"
  | "packVariants"
  | "onPackSelect"
  | "onAddToCart"
  | "onQuantityChange"
>): UseProductCardReturn => {
  // Check if item is added to cart
  const isAddedToCart = useMemo(() => quantity > 0, [quantity]);

  // Handle add to cart
  const handleAddToCart = useCallback(() => {
    onAddToCart();
  }, [onAddToCart]);

  // Handle quantity increment
  const handleIncrement = useCallback(() => {
    onQuantityChange(quantity + 1);
  }, [quantity, onQuantityChange]);

  // Handle quantity decrement
  const handleDecrement = useCallback(() => {
    if (quantity > 1) {
      onQuantityChange(quantity - 1);
    } else {
      onQuantityChange(0);
    }
  }, [quantity, onQuantityChange]);

  // Handle pack selection
  const handlePackSelect = useCallback(
    (packId: string) => {
      onPackSelect(packId);
    },
    [onPackSelect]
  );

  // Get pack button class
  const getPackButtonClass = useCallback(
    (pack: PackVariant) => {
      return cn(
        "cursor-pointer flex-1 border-[1.5px] border-white rounded-[5px] text-[10px] font-obviously h-7.5 px-2 py-1 font-semibold uppercase",
        pack.id === activePackId && "bg-white"
      );
    },
    [activePackId]
  );

  // Get pack button style
  const getPackButtonStyle = useCallback(
    (pack: PackVariant) => {
      if (pack.id === activePackId) {
        return { color: primaryColor };
      }
      return { color: "white" };
    },
    [activePackId, primaryColor]
  );

  return {
    isAddedToCart,
    handleAddToCart,
    handleIncrement,
    handleDecrement,
    handlePackSelect,
    getPackButtonClass,
    getPackButtonStyle,
  };
};
