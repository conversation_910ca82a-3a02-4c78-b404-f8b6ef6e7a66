# ProductCardV1 Component

A reusable product card component extracted from ProductCarouselSection with pack selection, add to cart functionality, and quantity management. Works with the existing Product interface like AccentProductCard.

## Features

- **Pack Selection Logic**: Active/inactive states for pack variants (1kg, 2kg, etc.)
- **Add to Cart Logic**: Conditional button rendering based on quantity
- **Quantity Management**: Increment/decrement controls when item is in cart
- **Design Preservation**: Maintains exact 278px width and visual hierarchy
- **Consistent Heights**: All cards maintain the same height regardless of content variations
- **Reusability**: Can be used across different sections
- **Product Interface**: Works with existing Product interface from ProductCard
- **Internal State Management**: Handles variant selection and cart state internally

## Usage

```tsx
import ProductCardV1 from "@/components/Common/ProductCardV1";
import { Product } from "@/components/Common/ProductCard";

// Define product data using existing Product interface
const productData: Product = {
  id: "cold-coffee-protein",
  title: "Cold Coffee 15 g Protein Powder - Pack of 1 KG",
  image: "/product-double-cocoa.png",
  price: 4443,
  originalPrice: 4799,
  rating: 4.5,
  weight: "1 KG",
  primaryColor: "#036A38",
  enableProductBg: true,
  sameDayDelivery: true,
  variants: [
    {
      id: "variant-1kg",
      size: "pack of 1 kg",
      price: 4443,
      originalPrice: 4799,
      weight: "1kg",
      image: "/product-double-cocoa.png",
    },
    {
      id: "variant-2kg",
      size: "pack of 2 kg",
      price: 8443,
      originalPrice: 8999,
      weight: "2kg",
      image: "/product-double-cocoa-2kg.png",
    },
  ],
};

// Use the component
<ProductCardV1 product={productData} />;
```

## Props

### Required Props

| Prop      | Type      | Description                                   |
| --------- | --------- | --------------------------------------------- |
| `product` | `Product` | Product data using existing Product interface |

### Optional Props

| Prop        | Type     | Default | Description            |
| ----------- | -------- | ------- | ---------------------- |
| `className` | `string` | -       | Additional CSS classes |

## Product Interface

Uses the existing `Product` interface from `@/components/Common/ProductCard`:

```tsx
interface Product {
  id: string;
  title: string;
  image: string;
  price: number;
  originalPrice?: number;
  rating: number;
  weight: string;
  primaryColor?: string;
  enableProductBg?: boolean;
  sameDayDelivery?: boolean;
  variants?: ProductVariant[];
  deliveryTime?: {
    hours: number;
    minutes: number;
    seconds: number;
  };
  // ... other properties
}
```

## Consistent Height Implementation

The component ensures all cards have the same height regardless of content variations:

### Fixed Height Containers

- **Product Title**: 52px fixed height (supports up to 2 lines with line-clamp-2)
- **Same Day Delivery**: 24px reserved space (shows icon or empty placeholder)
- **Pricing Section**: 20px fixed height for price information
- **Pack Selection**: 30px fixed height for variant buttons
- **Cart Button**: 32px fixed height for add to cart/quantity selector

### Layout Structure

```tsx
<div className="min-h-[200px]">
  {" "}
  {/* Fixed minimum height */}
  <div className="h-[52px]">Product Title</div>
  <div className="h-[24px]">Same Day Delivery or Placeholder</div>
  <div className="h-[20px]">Pricing</div>
  <div className="flex-grow"></div> {/* Spacer pushes content to bottom */}
  <div className="mt-auto">
    <div className="h-[30px]">Pack Selection</div>
    <div className="h-[32px]">Cart Button</div>
  </div>
</div>
```

### Benefits

- **Consistent Grid**: All cards align perfectly in carousel/grid layouts
- **Professional Look**: No jagged heights due to content variations
- **Predictable Layout**: Same visual structure regardless of data
- **Better UX**: Users can easily scan and compare products

## State Management

The component uses a custom hook `useProductCard` for internal logic:

- **Pack Selection**: Handles active/inactive states with proper styling
- **Cart Logic**: Manages add to cart vs quantity selector display
- **Event Handling**: Provides optimized event handlers with useCallback

## Styling

- **Width**: Fixed 278px width for consistency
- **Colors**: Uses `primaryColor` prop for theming
- **Pack Buttons**:
  - Active: White background with colored text
  - Inactive: Transparent background with white text and border
- **Cart Buttons**: White background with colored text

## Integration with ProductCarouselSection

To replace the existing inline card in ProductCarouselSection:

1. Import the component
2. Set up state management for pack selection and cart
3. Replace the inline JSX with ProductCardV1 component
4. Pass appropriate props and event handlers

## Files Structure

```
src/components/Common/ProductCardV1/
├── index.tsx           # Main component
├── types.ts           # TypeScript interfaces
├── hooks/
│   └── useProductCard.ts # Custom hook for state management
├── example.tsx        # Usage example
└── README.md          # This documentation
```
