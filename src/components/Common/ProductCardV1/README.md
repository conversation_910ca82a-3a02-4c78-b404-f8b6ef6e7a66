# ProductCardV1 Component

A reusable product card component extracted from ProductCarouselSection with pack selection, add to cart functionality, and quantity management. Works with the existing Product interface like AccentProductCard.

## Features

- **Pack Selection Logic**: Active/inactive states for pack variants (1kg, 2kg, etc.)
- **Add to Cart Logic**: Conditional button rendering based on quantity
- **Quantity Management**: Increment/decrement controls when item is in cart
- **Design Preservation**: Maintains exact 278px width and visual hierarchy
- **Reusability**: Can be used across different sections
- **Product Interface**: Works with existing Product interface from ProductCard
- **Internal State Management**: Handles variant selection and cart state internally

## Usage

```tsx
import ProductCardV1 from "@/components/Common/ProductCardV1";
import { Product } from "@/components/Common/ProductCard";

// Define product data using existing Product interface
const productData: Product = {
  id: "cold-coffee-protein",
  title: "Cold Coffee 15 g Protein Powder - Pack of 1 KG",
  image: "/product-double-cocoa.png",
  price: 4443,
  originalPrice: 4799,
  rating: 4.5,
  weight: "1 KG",
  primaryColor: "#036A38",
  enableProductBg: true,
  sameDayDelivery: true,
  variants: [
    {
      id: "variant-1kg",
      size: "pack of 1 kg",
      price: 4443,
      originalPrice: 4799,
      weight: "1kg",
      image: "/product-double-cocoa.png",
    },
    {
      id: "variant-2kg",
      size: "pack of 2 kg",
      price: 8443,
      originalPrice: 8999,
      weight: "2kg",
      image: "/product-double-cocoa-2kg.png",
    },
  ],
};

// Use the component
<ProductCardV1 product={productData} />;
```

## Props

### Required Props

| Prop      | Type      | Description                                   |
| --------- | --------- | --------------------------------------------- |
| `product` | `Product` | Product data using existing Product interface |

### Optional Props

| Prop        | Type     | Default | Description            |
| ----------- | -------- | ------- | ---------------------- |
| `className` | `string` | -       | Additional CSS classes |

## Product Interface

Uses the existing `Product` interface from `@/components/Common/ProductCard`:

```tsx
interface Product {
  id: string;
  title: string;
  image: string;
  price: number;
  originalPrice?: number;
  rating: number;
  weight: string;
  primaryColor?: string;
  enableProductBg?: boolean;
  sameDayDelivery?: boolean;
  variants?: ProductVariant[];
  deliveryTime?: {
    hours: number;
    minutes: number;
    seconds: number;
  };
  // ... other properties
}
```

## State Management

The component uses a custom hook `useProductCard` for internal logic:

- **Pack Selection**: Handles active/inactive states with proper styling
- **Cart Logic**: Manages add to cart vs quantity selector display
- **Event Handling**: Provides optimized event handlers with useCallback

## Styling

- **Width**: Fixed 278px width for consistency
- **Colors**: Uses `primaryColor` prop for theming
- **Pack Buttons**:
  - Active: White background with colored text
  - Inactive: Transparent background with white text and border
- **Cart Buttons**: White background with colored text

## Integration with ProductCarouselSection

To replace the existing inline card in ProductCarouselSection:

1. Import the component
2. Set up state management for pack selection and cart
3. Replace the inline JSX with ProductCardV1 component
4. Pass appropriate props and event handlers

## Files Structure

```
src/components/Common/ProductCardV1/
├── index.tsx           # Main component
├── types.ts           # TypeScript interfaces
├── hooks/
│   └── useProductCard.ts # Custom hook for state management
├── example.tsx        # Usage example
└── README.md          # This documentation
```
