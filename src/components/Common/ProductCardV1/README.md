# ProductCardV1 Component

A reusable product card component extracted from ProductCarouselSection with pack selection, add to cart functionality, and quantity management.

## Features

- **Pack Selection Logic**: Active/inactive states for pack variants (1kg, 2kg, etc.)
- **Add to Cart Logic**: Conditional button rendering based on quantity
- **Quantity Management**: Increment/decrement controls when item is in cart
- **Design Preservation**: Maintains exact 278px width and visual hierarchy
- **Reusability**: Can be used across different sections

## Usage

```tsx
import ProductCardV1 from "@/components/Common/ProductCardV1";
import { PackVariant } from "@/components/Common/ProductCardV1/types";

// Define pack variants
const packVariants: PackVariant[] = [
  {
    id: "pack-1kg",
    label: "pack of 1 kg",
    value: "1kg",
    isActive: activePackId === "pack-1kg",
  },
  {
    id: "pack-2kg",
    label: "pack of 2 kg", 
    value: "2kg",
    isActive: activePackId === "pack-2kg",
  },
];

// Use the component
<ProductCardV1
  id="cold-coffee-protein"
  title="Cold Coffee 15 g Protein Powder - Pack of 1 KG"
  image="/product-double-cocoa.png"
  price={4443}
  originalPrice={4799}
  primaryColor="#036A38"
  packVariants={packVariants}
  activePackId={activePackId}
  quantity={quantity}
  onPackSelect={handlePackSelect}
  onAddToCart={handleAddToCart}
  onQuantityChange={handleQuantityChange}
  showSameDayDelivery={true}
/>
```

## Props

### Required Props

| Prop | Type | Description |
|------|------|-------------|
| `id` | `string` | Unique product identifier |
| `title` | `string` | Product title |
| `image` | `string` | Product image URL |
| `price` | `number` | Current price |
| `primaryColor` | `string` | Primary color for styling |
| `packVariants` | `PackVariant[]` | Array of pack variants |
| `activePackId` | `string` | Currently selected pack ID |
| `quantity` | `number` | Current quantity in cart |
| `onPackSelect` | `(packId: string) => void` | Pack selection handler |
| `onAddToCart` | `() => void` | Add to cart handler |
| `onQuantityChange` | `(quantity: number) => void` | Quantity change handler |

### Optional Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `originalPrice` | `number` | - | Original price before discount |
| `className` | `string` | - | Additional CSS classes |
| `showSameDayDelivery` | `boolean` | `true` | Show same day delivery badge |
| `discountPercentage` | `number` | - | Manual discount percentage |

## Pack Variant Interface

```tsx
interface PackVariant {
  id: string;        // Unique identifier
  label: string;     // Display label (e.g., "pack of 1 kg")
  value: string;     // Value (e.g., "1kg")
  isActive: boolean; // Whether this variant is active
}
```

## State Management

The component uses a custom hook `useProductCard` for internal logic:

- **Pack Selection**: Handles active/inactive states with proper styling
- **Cart Logic**: Manages add to cart vs quantity selector display
- **Event Handling**: Provides optimized event handlers with useCallback

## Styling

- **Width**: Fixed 278px width for consistency
- **Colors**: Uses `primaryColor` prop for theming
- **Pack Buttons**: 
  - Active: White background with colored text
  - Inactive: Transparent background with white text and border
- **Cart Buttons**: White background with colored text

## Integration with ProductCarouselSection

To replace the existing inline card in ProductCarouselSection:

1. Import the component
2. Set up state management for pack selection and cart
3. Replace the inline JSX with ProductCardV1 component
4. Pass appropriate props and event handlers

## Files Structure

```
src/components/Common/ProductCardV1/
├── index.tsx           # Main component
├── types.ts           # TypeScript interfaces
├── hooks/
│   └── useProductCard.ts # Custom hook for state management
├── example.tsx        # Usage example
└── README.md          # This documentation
```
