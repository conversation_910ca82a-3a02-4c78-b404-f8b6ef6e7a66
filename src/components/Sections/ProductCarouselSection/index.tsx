"use client";
import React from "react";
import { UniversalCarousel } from "@/components/Common/CarouselWrapper.tsx";
import AccentProductCard from "@/components/Common/AccentProductCard";
import SeactionHeading from "@/components/Common/Section/Heading";
import { Product } from "@/components/Common/ProductCard";
import { cn } from "@/libs/utils";
import Image from "next/image";
import Link from "next/link";
import { SameDayDelivery } from "@/assets/icons/SDD";
import Plus from "@/assets/icons/Plus";
import Minus from "@/assets/icons/Minus";

interface ProductCarouselSectionProps {
  title: string;
  products: Product[];
  backgroundColor?: string;
  titleColor?: string;
  hideScrollbar?: boolean;
  scrollbarColor?: string;
  scrollBarTrackColor?: string;
  className?: string;
  enableProductBg?: boolean;
}

/**
 * A reusable section component that displays products in a carousel
 */
const ProductCarouselSection: React.FC<ProductCarouselSectionProps> = ({
  title,
  products,
  backgroundColor = "#EFA146",
  titleColor = "#FFFFFF",
  hideScrollbar = true,
  scrollbarColor,
  scrollBarTrackColor,
  className,
  enableProductBg = true,
}) => {
  return (
    <div className={cn("py-8", className)} style={{ backgroundColor }}>
      <div className="max-w-6xl mx-auto px-4">
        <SeactionHeading title={title} color={titleColor} />
        <div className="md:mt-7.5 mt-5">
          <UniversalCarousel
            useNativeScrollbar={true}
            hideScrollbar={hideScrollbar}
            scrollbarColor={scrollbarColor}
            scrollBarTrackColor={scrollBarTrackColor}
          >
            {products.map((product) => {
              // Create a copy of the product and modify enableProductBg
              const productWithBg = {
                ...product,
                enableProductBg: enableProductBg,
              };

              return (
                <div
                  key={product.id}
                  className="w-[278px] flex-shrink-0 rounded-[10px]"
                >
                  <div className="aspect-square relative">
                    <Image
                      src={product.image}
                      alt={product.title}
                      fill
                      objectFit="contain"
                      className="overflow-hidden rounded-t-[10px]"
                    />
                  </div>
                  <div
                    className="rounded-b-[10px] h-full flex flex-col gap-2 p-4 text-white"
                    style={{
                      backgroundColor: product.primaryColor || "transparent",
                    }}
                  >
                    <div className="flex flex-col gap-2 justify-between h-full text-white">
                      <Link
                        href={product.id}
                        className="text-lg leading-6.5 font-narrow font-semibold overflow-ellipsis break-words line-clamp-2 overflow-hidden"
                      >
                        Cold Coffee 15 g Protein Powder - Pack of 1 KG
                      </Link>
                      <SameDayDelivery
                        color={product.primaryColor}
                        height={24}
                        width={127}
                      />
                      <div className="text-white font-obviously text-[13px] leading-5 font-medium space-x-1">
                        <span className="line-through">
                          ₹{product.originalPrice}
                        </span>
                        <span>₹{product.price}</span>
                        <span>(7% OFF)</span>
                      </div>
                      <div className="space-y-2">
                        <div className="flex gap-1">
                          <button
                            className={cn(
                              "cursor-pointer flex-1 border-[1.5px] border-white rounded-[5px] text-[10px] text-white font-obviously h-7.5 px-2 py-1 font-semibold uppercase",
                              true && "bg-white"
                            )}
                            style={{ color: product.primaryColor }}
                          >
                            pack of 1 kg
                          </button>
                          <button
                            className={cn(
                              "cursor-pointer flex-1 border-[1.5px] border-white rounded-[5px] text-[10px] text-white font-obviously h-7.5 px-2 py-1 font-semibold uppercase"
                            )}
                          >
                            pack of 2 kg
                          </button>
                        </div>
                        <button
                          className="bg-white h-8 font-obviously flex items-center justify-center rounded-sm cursor-pointer w-full py-0.5 px-4 border border-white text-xs leading-8 font-semibold"
                          style={{ color: product.primaryColor }}
                        >
                          Add to cart
                        </button>
                        <button
                          className="bg-white h-8 font-obviously flex items-center justify-between rounded-sm cursor-pointer w-full py-2 px-4 border border-white text-xs leading-8 font-semibold"
                          style={{ color: product.primaryColor }}
                        >
                          <div className="p-2 flex items-center justify-center">
                            <Minus color={product.primaryColor} />
                          </div>
                          <span className="p-2 text-sm leading-4 font-bold">
                            1
                          </span>
                          <div className="p-2 flex items-center justify-center">
                            <Plus color={product.primaryColor} />
                          </div>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                // <AccentProductCard key={product.id} product={productWithBg} />
              );
            })}
          </UniversalCarousel>
        </div>
      </div>
    </div>
  );
};

export default ProductCarouselSection;
