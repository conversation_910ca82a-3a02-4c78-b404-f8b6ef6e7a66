"use client";
import React from "react";
import { UniversalCarousel } from "@/components/Common/CarouselWrapper.tsx";
import SeactionHeading from "@/components/Common/Section/Heading";
import { Product } from "@/components/Common/ProductCard";
import { cn } from "@/libs/utils";
import ProductCardV1 from "@/components/Common/ProductCardV1";

interface ProductCarouselSectionProps {
  title: string;
  products: Product[];
  backgroundColor?: string;
  titleColor?: string;
  hideScrollbar?: boolean;
  scrollbarColor?: string;
  scrollBarTrackColor?: string;
  className?: string;
  enableProductBg?: boolean;
}

/**
 * A reusable section component that displays products in a carousel
 */
const ProductCarouselSection: React.FC<ProductCarouselSectionProps> = ({
  title,
  products,
  backgroundColor = "#EFA146",
  titleColor = "#FFFFFF",
  hideScrollbar = true,
  scrollbarColor,
  scrollBarTrackColor,
  className,
  enableProductBg = true,
}) => {
  return (
    <div className={cn("py-8", className)} style={{ backgroundColor }}>
      <div className="max-w-6xl mx-auto px-4">
        <SeactionHeading title={title} color={titleColor} />
        <div className="md:mt-7.5 mt-5">
          <UniversalCarousel
            useNativeScrollbar={true}
            hideScrollbar={hideScrollbar}
            scrollbarColor={scrollbarColor}
            scrollBarTrackColor={scrollBarTrackColor}
          >
            {products.map((product) => {
              // Create a copy of the product and modify enableProductBg
              const productWithBg = {
                ...product,
                enableProductBg: enableProductBg,
              };

              return (
                <ProductCardV1 key={product.id} product={productWithBg} />
                // <AccentProductCard key={product.id} product={productWithBg} />
              );
            })}
          </UniversalCarousel>
        </div>
      </div>
    </div>
  );
};

export default ProductCarouselSection;
